<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beyond - Olhão</title>
    <link rel="icon" type="image/x-icon" href="../../Fotos/Logos/Favicon-Blanco.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <link rel="stylesheet" href="../../style.css">
</head>

        <body>
<!--Menu-->
            <header class="main-header" style="z-index: 1000;">
                <div class="container-fluid">
                    <nav class="navbar navbar-expand-lg">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav w-100 d-flex justify-content-center align-items-center">
                                <li class="nav-item menu-item">
                                    <a class="nav-link" href="../../destinos.html">DESTINOS</a>
                                </li>
                                <li class="nav-item menu-item">
                                    <a class="nav-link" href="../../motivos.html">MOTIVOS</a>
                                </li>
                                <li class="nav-item logo-item">
                                    <a class="nav-link" href="../../index.html">
                                        <img src="../../Fotos/Logos/Logo-Negro.png" alt="BEYOND" class="logo-img">
                                    </a>
                                </li>
                                <li class="nav-item menu-item">
                                    <a class="nav-link" href="../../sobre nosotros.html">NOSOTROS</a>
                                </li>
                                <li class="nav-item menu-item">
                                    <a class="nav-link" href="../../contacto.html">CONTACTO</a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </header>

<!-- Imagen del Caribe a pantalla completa -->
<div class="container-fluid p-0" style="position: relative; margin-top: 0; padding-top: 0;">
    <div class="row g-0">
        <div class="col-12" style="margin-top: 0; padding-top: 0;">
            <img src="../FOTOS DESTINOS/olhao 02.webp" alt="Olhão" class="img-fluid w-100" style="height: 100vh; object-fit: cover; margin-top: 0; position: relative; z-index: 0;">
        </div>
    </div>
</div>

<!-- Contenido del Caribe -->
<div class="container mt-5 pt-5">
    <div class="row">
        <div class="col-12 text-center">
            <h2 class="mb-4" style="font-family: 'Menu', sans-serif; font-size: 40px;">OLHÃO</h2>
        </div>
    </div>

    <!-- Contenido con texto a la izquierda y fotos a la derecha -->
    <div class="row mt-4">
        <!-- Columna de texto a la izquierda con posición fija -->
        <div class="col-md-6">
            <div class="sticky-text-container">
                <div class="text-content">
                    <p style="font-size: 18px;">Olhão, la auténtica joya del Algarve oriental, es una ciudad pesquera donde la tradición marinera, la belleza costera y la cultura portuguesa se funden en un escenario genuino y sin artificios. Desde su pintoresco puerto hasta su casco antiguo de inspiración morisca, pasando por sus mercados rebosantes de productos frescos y sus vistas a la Ría Formosa, esta localidad portuguesa ofrece experiencias inolvidables para todo tipo de viajeros, desde amantes de la gastronomía y la autenticidad hasta buscadores de naturaleza virgen e islas paradisíacas, consolidando su reputación como uno de los destinos más auténticos y menos masificados del sur de Portugal.</p>

                    <p style="font-size: 18px;">Barrios como el Bairro da Barreta y el Bairro do Levante combinan la arquitectura tradicional cubista con una tranquila vida local. La Avenida 5 de Outubro, con sus cafés y tiendas familiares, destaca por su ambiente genuino y ritmo pausado. Mientras tanto, zonas como el puerto pesquero, los mercados municipales y los embarcaderos hacia las islas ofrecen experiencias complementarias, cada una con su propio carácter distintivo, desde la animada subasta de pescado al amanecer hasta tranquilas travesías hacia playas vírgenes de arena blanca.</p>

                    <p style="font-size: 18px;">La naturaleza en Olhão es un espectáculo de biodiversidad mediterránea. El Parque Natural de la Ría Formosa, con su laberinto de canales, marismas, islas barrera y lagunas, ofrece un ecosistema único donde conviven cientos de especies de aves, peces y moluscos. Las islas de Armona, Culatra y Farol, accesibles solo en barco, presentan playas inmaculadas donde el dorado de la arena contrasta con el azul intenso del Atlántico. Los atardeceres sobre la ría, con sus cambiantes tonalidades y el vuelo de las aves marinas, crean un espectáculo natural que complementa perfectamente el carácter marinero de la ciudad.</p>

                    <p style="font-size: 18px;">La cultura olhanense, marcada por su profunda tradición pesquera y su pasado morisco, es una fascinante mezcla de influencias portuguesas, árabes y mediterráneas. Esta riqueza cultural se refleja en sus festivales como la Festa do Marisco, sus tradiciones marineras centenarias, sus expresiones artísticas desde la azulejería hasta la arquitectura cubista única en Portugal. El espíritu de sus habitantes, que han mantenido su identidad a través de siglos dedicados al mar, se caracteriza por su autenticidad, laboriosidad y esa capacidad única de preservar sus tradiciones mientras se adaptan a los nuevos tiempos sin perder su esencia.</p>

                    <p style="font-size: 18px;">Para los amantes de las experiencias auténticas, Olhão es un verdadero paraíso por descubrir: visitas al mercado de pescado al amanecer para ver la subasta tradicional, travesías en barco hacia las islas deshabitadas, degustaciones de mariscos y pescados recién capturados en tascas familiares, paseos por el laberinto de callejuelas de inspiración morisca, o la experiencia única de hospedarse en antiguas casas de pescadores reconvertidas en acogedores alojamientos. Y no podemos olvidar su exquisita gastronomía, desde la cataplana de mariscos y el arroz de lingueirão hasta las almejas a la Bulhão Pato, pasando por sus pastelerías tradicionales donde se elaboran los dulces de almendra y higo típicos del Algarve, reflejando perfectamente el espíritu marinero y la autenticidad de esta fascinante ciudad portuguesa.</p>

                    <div class="mt-4 mb-5">
                        <a href="../../contacto.html" class="btn btn-dark" style="font-family: 'Menu', sans-serif;">ORGANIZA TU VIAJE</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Columna de imágenes a la derecha con animación al scroll -->
        <div class="col-md-6">
            <div class="image-gallery" style="min-height: 120vh; padding-bottom: 100px;">
                <!-- Primera imagen con efecto de aparición -->
                <div class="image-container" data-aos="fade-left" data-aos-duration="1000" data-aos-offset="300">
                    <img src="../FOTOS DESTINOS/olhao 01.jpg" alt="Olhão" class="img-fluid w-100 mb-5" style="height: 400px; object-fit: cover; border-radius: 8px;">
                </div>

                <!-- Segunda imagen con efecto de aparición retrasado -->
                <div class="image-container" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200" data-aos-offset="300">
                    <img src="../FOTOS DESTINOS/olhão.webp" alt="Olhão" class="img-fluid w-100 mb-5" style="height: 300px; object-fit: cover; border-radius: 8px;">
                </div>

                <!-- Tercera imagen con efecto de aparición más retrasado -->
                <div class="image-container" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="400" data-aos-offset="300">
                    <img src="../FOTOS DESTINOS/namur 04.jpg" alt="Olhão" class="img-fluid w-100 mb-5" style="height: 350px; object-fit: cover; border-radius: 8px;">
            </div>
        </div>
    </div>

    <!-- Añadir librería AOS para animaciones al scroll -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <script>
        // Inicializar las animaciones AOS con opciones personalizadas
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                once: false,        // Las animaciones se repiten cada vez que se hace scroll
                mirror: true,       // Los elementos se animan al salir de la vista también
                anchorPlacement: 'top-center', // Ancla la animación al centro superior del elemento
                easing: 'ease-out-back' // Tipo de animación más suave
            });
        });
    </script>

</div>

   <!-- Logos Pormociones -->
<div class="text-center mb-4 mt-5">
    <h2 class="motivos-title">COLABORAMOS CON:</h2>
  </div>
  </div>
  <section class="promociones">
    <div class="container">
      <div class="row">
        <div class="col-md-6 text-center">
          <div class="promo-box">
            <img src="../../Fotos/Logos Promocion/OPHELO LOGO REG_NAVY.png" alt="Logo Ophelo" class="promo-img">
          </div>
        </div>
        <div class="col-md-6 text-center">
          <div class="promo-box">
            <img src="../../Fotos/Logos Promocion/GeekVerse.png" alt="Logo GeekVerse" class="promo-img">
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Cuadrado Azul Suscribete -->
  <section class="cuadro suscripcion">
    <div class="container">
      <div class="row">
        <div class="col-md-6">
          <h3 class="suscripcion-titulo">Suscríbete a nuestro cuaderno de viajes</h3>
          <p class="suscripcion-texto">Sé el primero en descubrir las últimas novedades en tendencias de viajes.</p>
        </div>
        <div class="col-md-6">
          <form class="suscripcion-form">
            <div class="row mb-3">
              <div class="col-md-6">
                <input type="text" class="form-control" placeholder="Nombre">
              </div>
              <div class="col-md-6">
                <input type="email" class="form-control" placeholder="Email">
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-12">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="politicaCheck">
                  <label class="form-check-label" for="politicaCheck">
                    He leído y acepto la Política de Privacidad y puedo anular mi suscripción.
                  </label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12 text-end">
                <button type="submit" class="btn btn-light suscribirme-btn">SUSCRIBIRME</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="row">
        <div class="col-md-4 text-center text-md-start">
          <a class="nav-link" href="index.html">
            <img src="../../Fotos/Logos/Logo-Negro.png" alt="BEYOND" class="logo-img">
        </a>
          <div class="footer-social">
            <h3>¡SÍGUENOS!</h3>
            <div class="social-icons">
              <a href="https://www.instagram.com/dreamwithbeyond/" target="_blank" class="social-icon"><i class="bi bi-instagram"></i></a>
              <a href="#" target="_blank" class="social-icon"><i class="bi bi-facebook"></i></a>
              <a href="#" target="_blank" class="social-icon"><i class="bi bi-youtube"></i></a>
              <a href="#" target="_blank" class="social-icon"><i class="bi bi-twitter-x"></i></a>
            </div>
          </div>
        </div>

        <div class="col-md-4 footer-nav">
          <ul class="footer-links">
            <li><a href="../../destinos.html">DESTINOS</a></li>
            <li><a href="../../motivos.html">MOTIVOS</a></li>
            <li><a href="../../sobre nosotros.html">NOSOTROS</a></li>
            <li><a href="../../contacto.html">CONTACTO</a></li>
          </ul>
        </div>

        <div class="col-md-4 footer-subscribe">
          <h3>Suscríbete a nuestro cuaderno de viajes</h3>
          <p>Sé el primero en descubrir las últimas novedades en tendencias de viajes.</p>
          <form class="footer-form">
            <div class="form-row">
              <input type="text" placeholder="Nombre" class="footer-input">
              <input type="email" placeholder="Email" class="footer-input">
              <button type="submit" class="footer-btn">SUSCRIBIRME</button>
            </div>
            <div class="form-check">
              <input type="checkbox" id="footerCheck">
              <label for="footerCheck">He leído y acepto la Política de Privacidad</label>
            </div>
          </form>
        </div>
      </div>

      <div class="footer-bottom">
        <p>© 2025 Beyond Agencia de Viajes</p>
      </div>
    </div>
  </footer>

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">



                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" integrity="sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq" crossorigin="anonymous"></script>
                <script>
                  window.addEventListener('scroll', function() {
                      const header = document.querySelector('.main-header');
                      if (window.scrollY > 50) {
                          header.classList.add('scrolled');
                      } else {
                          header.classList.remove('scrolled');
                      }
                  });
              </script>
              </body>
  </html>