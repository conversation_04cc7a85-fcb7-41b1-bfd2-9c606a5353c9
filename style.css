
body{
    background-color: #FCF6E5;
}

h2{
    font-family: 'Menu', sans-serif;
}

/*Menu*/
.main-header {
  width: 100%;
  padding: 0;
  position: fixed !important;
  top: 0px;
  left: 0px;
  z-index: 10;
  background-size: cover;
  background-position: center;
  height: 120px; /* Aumentado para dar más espacio */
  transition: background-color 0.3s ease; /* Transición suave */
}

.main-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent; /* Inicialmente transparente */
  z-index: -1;
  transition: background-color 0.3s ease; /* Transición suave */
}

/* Clase que se añadirá con JavaScript al hacer scroll */
.main-header.scrolled::before {
  background-color: #FCF6E5;
}



.navbar {
    height: 100%;
    display: flex;
    align-items: center;
}

.navbar-collapse {
    display: flex;
    align-items: center;
}

.navbar-nav {
    gap: 2rem;
    padding-top: 15px; /* <PERSON><PERSON><PERSON><PERSON> para bajar los elementos */
}

.menu-item {
    text-align: center;
    display: flex;
    align-items: center;
}

.menu-item .nav-link {
    color: black;
    font-size: 1.5rem;
    font-family: 'Menu', sans-serif;
    padding-top: 10px; /* Añadido para alinear con el logo */
}

.logo-item {
    margin: 0 2rem;
    display: flex;
    align-items: center;
}

.logo-img {
    width: 200px;
    max-height: 80px;
    object-fit: contain;
    margin-top: 10px; /* Añadido para bajar el logo */
}

.nav-link:hover {
    text-decoration: underline;
}



/*fotos*/
.carousel{
    height: 100vh; /* Altura completa de la ventana */
    width: 100%;  /* Mantiene el ancho al 100% del contenedor */
    margin-bottom: 0; /* Eliminado el margen inferior para que no haya espacio */
    overflow: hidden; /* Para evitar desbordamientos */
}

/* Ajustar las imágenes del carousel */
.carousel-item img {
    object-fit: cover; /* Para que las imágenes cubran bien el espacio */
    height: 100vh; /* Altura completa de la ventana */
    display: block; /* Asegura que no haya espacio extra debajo de la imagen */
}

/*Cuadro azul */
.logo-cuadro{
    width: 100px;
    margin-top: 40px;
}

.cuadro {
    text-align: center;
    max-width: 1560px;
    width: 100%;
    height: 370px;
    background-color: #01083B;
    color: white;
    margin-top: -1px;
  }
  .parrafo-azul {
    font-family: 'Menu', sans-serif;
    font-size: 25px;
    margin: 30px;
    text-align: center;
    line-height: 1.4;
  }

/* Cajas destinos */
.destino-title, .motivos-title {
    font-family: 'Menu', sans-serif;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
}

.destino-link-box {
    text-decoration: none;
    color: inherit;
    display: block;
}

.destino-box {
    border: 1px solid #e0e0e0;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.destino-box:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-5px);
}

.foto-container {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    overflow: hidden;
}

.foto {
    font-size: 1.5rem;
    color: #6c757d;
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.foto-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.destino-label {
    padding: 10px;
    text-align: center;
    font-family: 'Menu', sans-serif;
    font-weight: bold;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
}

.ver-mas-btn {
    background-color: #343a40;
    border: none;
    padding: 8px 25px;
    border-radius: 0;
    font-family: 'Menu', sans-serif;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.ver-mas-btn:hover {
    background-color: #212529;
}

.nuevo-boton {
    background-color: #6c757d;
    border: none;
    padding: 8px 25px;
    border-radius: 0;
    font-family: 'Menu', sans-serif;
    text-transform: uppercase;
    font-size: 0.9rem;
    margin-top: 30px;
    transition: all 0.3s ease;
}

.nuevo-boton:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

/* Slider*/
.slider {
    overflow: hidden;
    width: 100%;
    height: 400px;
    position: relative;
  }

.slider-item img:hover{
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-5px);
}

  .slider-track {
    display: flex;
    width: calc(400px * 6); /* 6 items (3 originales duplicados) */
  animation: scroll 50s linear infinite;
  }

  .slider-link {
    text-decoration: none;
    color: black;
  }

  .slider-item {
    min-width: 400px;
    margin: 20px;
    background: white;
    text-align: center;
    border: 1px solid #ccc;
  }


  .slider-item img {
    width: 100%;
    height: 300px;
    object-fit: cover;
  }

  .slider-item p {
    margin: 0;
    padding: 10px;
    font-family: 'Menu', sans-serif;
    font-size: 18px;
  }

/* Logos Promocion*/
.promo-box  img{
    margin-top: 30px;
    width: 50%
  }

/* Sección de suscripción */
.cuadro.suscripcion {
  height: auto;
  padding: 40px 0;
  margin-top: 50px;
}

.suscripcion-titulo {
  font-family: 'Menu', sans-serif;
  font-size: 28px;
  color: white;
  margin-bottom: 15px;
  text-align: left;
}

.suscripcion-texto {
  font-family: 'Menu', sans-serif;
  font-size: 18px;
  color: white;
  text-align: left;
  line-height: 1.4;
}

.suscripcion-form {
  padding: 10px 0;
}

.suscripcion-form input {
  background-color: transparent;
  border: 1px solid white;
  color: white;
  padding: 10px 15px;
}

.suscripcion-form input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.form-check-label {
  color: white;
  font-size: 14px;
}

.form-check-input {
  border-color: white;
}

.suscribirme-btn {
  background-color: white;
  color: #01083B;
  font-family: 'Menu', sans-serif;
  font-weight: bold;
  padding: 8px 25px;
  border: none;
  transition: all 0.3s ease;
}

.suscribirme-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

 /* Footer */
.footer {
    background-color: #FCF6E5;
    padding: 50px 0 20px;
    border-top: 1px solid #e0e0e0;
    margin-top: 50px;
  }

  .footer-logo {
    font-family: 'Menu', sans-serif;
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .footer-social h3 {
    font-family: 'Menu', sans-serif;
    font-size: 40px;
    margin-bottom: 15px;
  }

  .social-icons {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
  }

  .social-icon {
    font-size: 2rem;
    color: #01083B;
    transition: all 0.3s ease;
  }

  .social-icon:hover {
    color: #0056b3;
    transform: translateY(-3px);
  }

  .footer-nav {
    display: flex;
    justify-content: center;
  }

  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-links li {
    margin-bottom: 15px;
  }

  .footer-links a {
    font-family: 'Menu', sans-serif;
    font-size: 1.1rem;
    color: #01083B;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .footer-links a:hover {
    text-decoration: underline;
  }

  .footer-subscribe {
    background-color: #01083B;
    padding: 20px;
    color: white;
    border-radius: 5px;
  }

  .footer-subscribe h3 {
    font-family: 'Menu', sans-serif;
    font-size: 1.2rem;
    margin-bottom: 10px;
  }

  .footer-subscribe p {
    font-size: 0.9rem;
    margin-bottom: 15px;
  }

  .footer-form .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
  }

  .footer-input {
    flex: 1;
    min-width: 120px;
    padding: 8px 12px;
    border: none;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .footer-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  .footer-btn {
    background-color: white;
    color: #01083B;
    border: none;
    padding: 8px 15px;
    font-family: 'Menu', sans-serif;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .footer-btn:hover {
    background-color: #f0f0f0;
  }

  .form-check {
    margin-top: 10px;
    font-size: 0.8rem;
  }

  .footer-bottom {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
    font-size: 0.9rem;
    color: #666;
  }



/* PAGINA DE DESTINOS */
.destinos {
  font-family: 'Menu', sans-serif;
  font-size: 40px;
  margin-top: 50px;
}
/* Estilos para la sección de continentes */
.continentes-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 50px;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.continente-card {
  flex: 1;
  height: 620px;
  position: relative;
  border-right: 1px solid #e0e0e0;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s ease;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-width: 0; /* Permite que las tarjetas se reduzcan si es necesario */
}

.continente-card:last-child {
  border-right: none;
}

/* Clases específicas para cada continente */
.continente-america {
  background-image: url('Fotos/nueva york.jpg');
}

.continente-europa {
  background-image: url('Fotos/paris.jpg');
}

.continente-africa {
  background-image: url('Fotos/africa\ destinos.webp');
}

.continente-asia {
  background-image: url('Fotos/asia\ destinos.jpg');
}

.continente-oceania {
  background-image: url('Fotos/oceania\ destinos.jpg');
}

.continente-titulo {
  font-family: 'Menu', sans-serif;
  text-align: center;
  padding: 20px 0;
  transition: transform 0.3s ease;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  background-color: rgba(0, 0, 0, 0.3);
  margin: 0;
  position: relative;
  z-index: 1;
}

.continente-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(1, 8, 59, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transform: translateY(100%);
  transition: all 0.4s ease;
}

.continente-card:hover .continente-overlay {
  opacity: 1;
  transform: translateY(0);
}

.continente-card:hover .continente-titulo {
  transform: translateY(-20px);
}

.continente-destinos {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.destino-link {
  color: white;
  font-family: 'Menu', sans-serif;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.destino-link:hover {
  transform: scale(1.1);
  text-decoration: underline;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.destino-link {
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
  text-align: center;
}

.destino-link:nth-child(1) { animation-delay: 0.1s; }
.destino-link:nth-child(2) { animation-delay: 0.2s; }
.destino-link:nth-child(3) { animation-delay: 0.3s; }

/* Estilos para la cuadrícula de motivos */
.motivos-viajar{
  margin-top: 50px;
  font-family: 'Menu', sans-serif;
  font-size: 40px;
}

.motivos-grid {
  margin: 30px 0;
}

.motivo-card {
  border: 1px solid #e0e0e0;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.4s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.motivo-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.motivo-card:hover .motivo-foto {
  background-color: #f0f0f0;
}

.motivo-card:hover .motivo-texto {
  color: #01083B;
}

.motivo-foto {
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  font-size: 24px;
  color: #333;
  font-family: 'Menu', sans-serif;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.motivo-foto::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #01083B;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s ease;
}

.motivo-card:hover .motivo-foto::after {
  transform: scaleX(1);
  transform-origin: left;
}

.motivo-texto {
  padding: 15px;
  text-align: center;
  font-family: 'Menu', sans-serif;
  font-weight: bold;
  font-size: 18px;
  background-color: white;
  transition: all 0.3s ease;
}

/* Tarjeta destacada */


.motivo-card.highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #01083B;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.motivo-card.highlight:hover::before {
  opacity: 0.05;
}

/* Animaciones para las tarjetas */
.motivo-card {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Aplicar animación con retraso a cada tarjeta */
.motivos-grid .row:nth-child(1) .col-md-4:nth-child(1) .motivo-card {
  animation-delay: 0.1s;
}
.motivos-grid .row:nth-child(1) .col-md-4:nth-child(2) .motivo-card {
  animation-delay: 0.2s;
}
.motivos-grid .row:nth-child(1) .col-md-4:nth-child(3) .motivo-card {
  animation-delay: 0.3s;
}
.motivos-grid .row:nth-child(2) .col-md-4:nth-child(1) .motivo-card {
  animation-delay: 0.4s;
}
.motivos-grid .row:nth-child(2) .col-md-4:nth-child(2) .motivo-card {
  animation-delay: 0.5s;
}
.motivos-grid .row:nth-child(2) .col-md-4:nth-child(3) .motivo-card {
  animation-delay: 0.6s;
}

/* SOBRE NOSOTROS */
.nosotros {
  font-family: 'Menu', sans-serif;
  font-size: 40px;
  margin-top: 50px;
  width: 100%;
}

.nosotros-contenido {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
  max-width: 1400px;
  padding: 0;
}

.texto-nosotros {
  width: 50%;
  padding: 0;
  box-sizing: border-box;
}

.texto-nosotros p {
  font-size: 1.1rem;
  color: #000000;
  line-height: 1.6;
  padding-right: 0;
}

.imagenes-nosotros {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0;
}

.img-nosotros {
  width: 75%;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  object-fit: cover;
  height: auto;
}



/* FORMULARIO DE CONTACTO */
.contact-form-section {
  margin-top: 160px;
  padding-bottom: 50px;
}

.contact-form-title {
  font-family: 'Menu', sans-serif;
  font-size: 2.5rem;
  margin-bottom: 30px;
  text-align: center;
  margin-top: 50px;
}

.contact-form-description {
  font-family: 'Menu', sans-serif;
  font-size: 1.2rem;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 40px;
}

.contact-form label {
  font-family: 'Menu', sans-serif;
  font-weight: bold;
  margin-bottom: 8px;
}

.contact-form .form-control {
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  padding: 10px 0;
  font-family: 'Menu', sans-serif;
}

.contact-form textarea.form-control {
  border: 1px solid #ced4da;
  padding: 10px;
}

.contact-form .btn-submit {
  background-color: white;
  border: 1px solid #000;
  padding: 8px 30px;
  font-family: 'Menu', sans-serif;
  font-weight: bold;
  transition: all 0.3s ease;
}

.contact-form .btn-submit:hover {
  background-color: #01083B;
  color: white;
  border-color: #01083B;
}


/* Estilos para páginas de continentes */
.continent-header {
  text-align: center;
  margin-top: 150px;
  margin-bottom: 30px;
}

.continent-header h1 {
  font-family: 'Menu', sans-serif;
  font-size: 40px;
}

.continent-content {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
  background-color: #01083B;
}

.continent-description {
  text-align: center;
  padding: 40px 20px;
}

.continent-description h2 {
  font-family: 'Menu', sans-serif;
  color: white;
  font-size: 2rem;
  margin-bottom: 20px;
}

.organize-trip {
  text-align: center;
  margin: 40px 0 50px;
}

.organize-btn {
  display: inline-block;
  background-color: #000;
  color: white;
  padding: 12px 24px;
  text-decoration: none;
  font-family: 'Menu', sans-serif;
  font-weight: bold;
  transition: all 0.3s ease;
}

.organize-btn:hover {
  background-color: #333;
  transform: translateY(-3px);
}

/* HOVER TEXTO DESTINOS AMERICA EUROPA ASIA OCEANIA AFRICA */
.hover-text-container {
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  background-color: #FCF6E5;
}

.hover-text-container:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.hover-text-trigger {
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hover-text-trigger h3 {
  font-family: 'Menu', sans-serif;
  margin: 0;
  color: #01083B;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.hover-text-trigger i {
  margin-left: 10px;
  transition: transform 0.3s ease;
}

.hover-text-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease, padding 0.5s ease;
  padding: 0 15px;
}

.hover-text-content p {
  font-family: 'Menu', sans-serif;
  line-height: 1.6;
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.hover-text-container:hover .hover-text-content {
  max-height: 300px;
  padding: 0 15px 15px;
}

.hover-text-container:hover .hover-text-trigger i {
  transform: rotate(180deg);
}

/* FOTO DESTINOS CADA PAIS Y ANIMACION DESTINOS SCROLL */
/* Estilos para la galería de imágenes */
        .image-gallery {
            padding-top: 20px;
        }

        .image-container {
            opacity: 0; 
        }

        /* Efecto de hover para las imágenes */
        .image-container img {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .image-container img:hover {
            transform: scale(1.03);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

       /* Estilos para el texto fijo */
        .sticky-text-container {
            position: sticky;
            top: 150px; /* Ajusta según la altura de tu header */
            padding-top: 20px;
            max-height: none; /* Elimina la altura máxima */
            overflow-y: visible; /* Elimina el scroll del texto */
        }

        .text-content {
            padding-right: 20px;
        }

        /* Estilos para la galería de imágenes */
        .image-gallery {
            padding-top: 20px;
            position: relative;
        }

        .image-container {
            opacity: 0; /* Inicialmente invisible */
            transition: all 0.5s ease;
        }

        /* Efecto de hover para las imágenes */
        .image-container img {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .image-container img:hover {
            transform: scale(1.03);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        /* Indicador de scroll */
        .arrow-down {
            width: 30px;
            height: 30px;
            border-right: 3px solid #666;
            border-bottom: 3px solid #666;
            transform: rotate(45deg);
            margin: 0 auto;
            animation: arrow-bounce 2s infinite;
        }

        @keyframes arrow-bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0) rotate(45deg);
            }
            40% {
                transform: translateY(-10px) rotate(45deg);
            }
            60% {
                transform: translateY(-5px) rotate(45deg);
            }
        }



        /* Crear un espacio después de las imágenes para forzar el scroll */
        .image-gallery::after {
            content: '';
            display: block;
            height: 50px; /* Espacio adicional después de las imágenes */
        }



  /* Responsive */
  @media (max-width: 767px) {
    .suscripcion-titulo, .suscripcion-texto {
      text-align: center;
      margin-bottom: 20px;
    }

    .suscripcion-form {
      padding-top: 20px;
    }

    .col-12.text-end {
      text-align: center !important;
      margin-top: 15px;
    }
  }

  @media (max-width: 767px) {
    .footer-logo, .footer-social, .footer-nav, .footer-subscribe {
      text-align: center;
      margin-bottom: 30px;
    }

    .social-icons {
      justify-content: center;
    }

    .footer-form .form-row {
      flex-direction: column;
    }

    .footer-input, .footer-btn {
      width: 100%;
      margin-bottom: 10px;
    }

    /* Responsive styles for contact form */
    .contact-form-section {
      margin-top: 120px;
      padding: 0 15px 30px;
    }

    .contact-form-title {
      font-size: 2rem;
    }

    .contact-form-description {
      font-size: 1rem;
    }

    .contact-form .btn-submit {
      width: 100%;
      margin-top: 10px;
    }
  }

  /* Animación del movimiento slider*/
  @keyframes scroll {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }


/* Responsive adjustments */
@media (max-width: 991.98px) {
    .main-header {
        height: auto;
        min-height: 140px;
    }

    .navbar-collapse {
        background-color: rgba(255, 255, 255, 0.9);
        padding: 1rem;
    }

    .navbar-nav {
        gap: 0.5rem;
        padding-top: 10px;
    }

    .menu-item .nav-link {
        font-size: 1.2rem;
        padding-top: 8px;
    }

    .logo-img {
        width: 150px;
        max-height: 60px;
        margin-top: 8px;
    }

    /* Responsive para continentes */
    .continentes-container {
        flex-direction: column;
    }

    .continente-card {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        height: 400px;
    }

    .continente-card:last-child {
        border-bottom: none;
    }

    /* Responsive para motivos */
    .motivos-viajar {
        margin-top: 120px;
        font-size: 32px;
    }

    .motivo-foto {
        height: 200px;
    }
}

@media (max-width: 575.98px) {
    .menu-item .nav-link {
        font-size: 1rem;
        padding-top: 5px;
    }

    .logo-img {
        width: 120px;
        max-height: 50px;
        margin-top: 5px;
    }

    /* Responsive para el texto desplegable */
    .hover-text-trigger h3 {
        font-size: 1.2rem;
    }

    .hover-text-content p {
        font-size: 1rem;
    }

    /* Ajuste para menú móvil */
    .navbar-toggler {
        margin-top: 10px;
    }

    /* Responsive para motivos en móvil */
    .motivos-viajar {
        margin-top: 100px;
        font-size: 28px;
    }

    .motivo-foto {
        height: 470px;
        font-size: 20px;
    }

    .motivo-texto {
        font-size: 16px;
        padding: 10px;
    }

    .motivo-card {
        margin-bottom: 15px;
    }

    .motivo-card:hover {
        transform: translateY(-5px);
    }
}

@font-face {
    font-family:Menu ;
    src: url(Fonts/texto\ menu.ttf);
}

@font-face{
  font-family:Parrafos;
  src: url(Fonts/univers/UniversLight.ttf);
}

p{
  font-family: Parrafos;
}

 .cls-2 {
        font-family: CrakeTest-Regular, 'Crake Test';
        font-size: 60px;
        justify-content: center;
        align-items: center;
        line-height: 1rem;
      }